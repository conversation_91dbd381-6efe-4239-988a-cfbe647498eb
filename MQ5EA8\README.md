# AutoTrader EA - Professional MT5 Expert Advisor

A comprehensive and customizable Expert Advisor for MetaTrader 5 that provides automated trading capabilities with robust risk management features and safety controls.

## 🎯 Project Overview

AutoTrader EA is a professional-grade Expert Advisor designed for MetaTrader 5 that combines automated trading logic with comprehensive risk management. The EA features a modular architecture that allows for easy customization while maintaining strict safety protocols to protect your trading capital.

### Key Features

- **Automated Trading**: Implements moving average crossover strategy with customizable parameters
- **Risk Management**: Built-in position sizing based on account risk percentage
- **Daily Limits**: Configurable daily profit/loss limits to control exposure
- **Trading Hours**: Flexible time-based trading restrictions
- **Safety Controls**: Multiple layers of protection including spread monitoring and position limits
- **Modular Design**: Separate utility functions for easy maintenance and expansion
- **Comprehensive Logging**: Detailed logging and notification system

### Capabilities

- Multi-timeframe signal detection
- Dynamic lot size calculation based on risk management
- Real-time position monitoring and management
- Session-based trading controls
- Emergency position closure functionality
- Customizable notification system (alerts, push notifications, email)

## 📦 Installation Instructions

### Step 1: Copy Files to MT5 Data Folder

1. **Locate your MT5 Data Folder**:
   - Open MetaTrader 5
   - Go to `File` → `Open Data Folder`
   - This will open your MT5 data directory

2. **Copy the EA Files**:
   ```
   Copy AutoTrader_EA.mq5 to: MQL5/Experts/
   Copy Include/TradeUtils.mqh to: MQL5/Include/
   Copy Presets/*.set to: MQL5/Presets/
   ```

3. **Verify File Structure**:
   ```
   MQL5/
   ├── Experts/
   │   └── AutoTrader_EA.mq5
   ├── Include/
   │   └── TradeUtils.mqh
   └── Presets/
       ├── Conservative.set
       └── Aggressive.set
   ```

### Step 2: Compilation in MetaEditor

1. **Open MetaEditor**:
   - In MT5, press `F4` or go to `Tools` → `MetaQuotes Language Editor`

2. **Open the EA File**:
   - In MetaEditor, go to `File` → `Open`
   - Navigate to `MQL5/Experts/AutoTrader_EA.mq5`

3. **Compile the EA**:
   - Press `F7` or click the `Compile` button
   - Check the `Errors` tab for any compilation issues
   - Successful compilation will show "0 error(s), 0 warning(s)"

4. **Verify Compilation**:
   - The compiled file `AutoTrader_EA.ex5` should appear in `MQL5/Experts/`

### Step 3: Attaching EA to Charts

1. **Open a Chart**:
   - Open the desired currency pair chart in MT5
   - Recommended timeframes: M15, H1, or H4

2. **Attach the EA**:
   - In the Navigator panel, expand `Expert Advisors`
   - Drag `AutoTrader_EA` onto the chart
   - The EA settings dialog will appear

3. **Configure Settings**:
   - Adjust parameters according to your preferences (see Configuration Guide below)
   - Click `OK` to attach the EA

4. **Verify Attachment**:
   - You should see a smiley face icon in the top-right corner of the chart
   - Check the `Experts` tab for initialization messages

## ⚙️ Configuration Guide

### Input Parameters Explanation

#### Trading Parameters
- **Lot Size** (0.1): Fixed lot size when risk management is disabled
- **Stop Loss** (50): Stop loss distance in pips
- **Take Profit** (100): Take profit distance in pips
- **Magic Number** (12345): Unique identifier for EA trades
- **Use Risk Management** (true): Enable dynamic lot sizing based on risk
- **Risk Percent per Trade** (2.0): Percentage of account balance to risk per trade

#### Trading Hours
- **Start Trading Hour** (8): Hour to start trading (0-23, server time)
- **End Trading Hour** (18): Hour to stop trading (0-23, server time)
- **Trade Monday to Friday Only** (true): Restrict trading to weekdays

#### Daily Limits
- **Use Daily Limits** (true): Enable daily profit/loss controls
- **Daily Profit Limit** (100.0): Stop trading after reaching this profit
- **Daily Loss Limit** (50.0): Stop trading after reaching this loss
- **Maximum Daily Trades** (10): Maximum number of trades per day

#### Strategy Settings
- **Fast Moving Average Period** (10): Period for fast MA
- **Slow Moving Average Period** (20): Period for slow MA
- **Moving Average Method** (EMA): Type of moving average calculation
- **Applied Price** (Close): Price used for MA calculation

#### Risk Controls
- **Maximum Spread** (3.0): Maximum allowed spread in pips
- **Maximum Slippage** (3): Maximum acceptable slippage in pips
- **Close on Friday** (true): Close all positions before weekend

### Recommended Settings by Account Type

#### Conservative Account (Small Balance: $500-$2,000)
```
Lot Size: 0.01
Risk Percent: 1.0%
Stop Loss: 50 pips
Take Profit: 100 pips
Daily Loss Limit: $20
Daily Profit Limit: $40
Max Daily Trades: 5
```

#### Standard Account (Medium Balance: $2,000-$10,000)
```
Lot Size: 0.1
Risk Percent: 2.0%
Stop Loss: 40 pips
Take Profit: 80 pips
Daily Loss Limit: $50
Daily Profit Limit: $100
Max Daily Trades: 8
```

#### Aggressive Account (Large Balance: $10,000+)
```
Lot Size: 0.5
Risk Percent: 3.0%
Stop Loss: 30 pips
Take Profit: 60 pips
Daily Loss Limit: $200
Daily Profit Limit: $400
Max Daily Trades: 15
```

### Risk Management Guidelines

1. **Never risk more than 2-3% per trade**
2. **Set daily loss limits to 1-2% of account balance**
3. **Use appropriate lot sizes for your account balance**
4. **Monitor drawdown and adjust parameters accordingly**
5. **Test thoroughly on demo accounts before live trading**

## 🚀 Usage Instructions

### Enabling Automated Trading in MT5

1. **Enable AutoTrading**:
   - Click the `AutoTrading` button in MT5 toolbar (should turn green)
   - Or go to `Tools` → `Options` → `Expert Advisors` → Check "Allow automated trading"

2. **Configure Expert Advisor Settings**:
   - Go to `Tools` → `Options` → `Expert Advisors`
   - Ensure the following are checked:
     - "Allow automated trading"
     - "Allow DLL imports" (if using external libraries)
     - "Allow imports of external experts"

### Setting Up the EA on Charts

1. **Choose Appropriate Timeframe**:
   - H1 (1-hour): Balanced approach, good for most strategies
   - M15 (15-minute): More frequent signals, higher activity
   - H4 (4-hour): Longer-term approach, fewer but stronger signals

2. **Monitor Initial Setup**:
   - Watch the `Experts` tab for initialization messages
   - Verify that the EA is loading indicators correctly
   - Check that trading hours and conditions are properly detected

3. **Load Preset Configurations**:
   - Right-click on the EA in the chart
   - Select `Expert Advisors` → `Properties`
   - Click `Load` and select a preset file (Conservative.set or Aggressive.set)

### Monitoring and Managing Trades

#### Real-time Monitoring
- **Experts Tab**: Monitor EA messages and trade execution logs
- **Trade Tab**: View open positions and their current status
- **Account History**: Review closed trades and performance

#### Key Metrics to Watch
- Daily profit/loss progress
- Number of trades executed
- Current drawdown levels
- Spread conditions and market volatility

#### Manual Intervention
- **Emergency Stop**: Remove EA from chart to stop all new trades
- **Close Positions**: Use the "Close All" function in extreme situations
- **Adjust Parameters**: Modify settings based on market conditions

## 🔧 Strategy Customization

### Modifying Signal Detection Logic

The default strategy uses moving average crossover. To customize:

1. **Locate the Signal Function**:
   ```mql5
   ENUM_ORDER_TYPE GetTradingSignal()
   {
       // Current MA crossover logic here
   }
   ```

2. **Add New Indicators**:
   ```mql5
   // Example: Adding RSI
   int rsiHandle = iRSI(Symbol(), PERIOD_CURRENT, 14, PRICE_CLOSE);
   double rsiBuffer[1];
   CopyBuffer(rsiHandle, 0, 0, 1, rsiBuffer);
   ```

3. **Implement New Conditions**:
   ```mql5
   // Example: RSI overbought/oversold
   if(rsiBuffer[0] < 30 && fastMA_current > slowMA_current)
       return ORDER_TYPE_BUY;
   if(rsiBuffer[0] > 70 && fastMA_current < slowMA_current)
       return ORDER_TYPE_SELL;
   ```

### Adding New Indicators

1. **Create Indicator Handles in OnInit()**:
   ```mql5
   int newIndicatorHandle = iCustomIndicator(Symbol(), PERIOD_CURRENT, "IndicatorName", parameters);
   ```

2. **Update Indicator Values in OnTick()**:
   ```mql5
   double indicatorBuffer[3];
   CopyBuffer(newIndicatorHandle, 0, 0, 3, indicatorBuffer);
   ```

3. **Integrate into Signal Logic**:
   ```mql5
   // Use indicator values in GetTradingSignal() function
   ```

### Implementing Different Trading Strategies

#### Breakout Strategy
```mql5
// Add support/resistance levels
// Detect breakouts above/below key levels
// Enter trades in breakout direction
```

#### Mean Reversion Strategy
```mql5
// Use Bollinger Bands or similar
// Enter trades when price reaches extreme levels
// Target return to mean
```

#### Trend Following Strategy
```mql5
// Use ADX for trend strength
// Enter trades in direction of strong trends
// Exit when trend weakens
```

## 🛡️ Safety Features

### Daily Profit/Loss Limits

The EA automatically monitors daily performance and stops trading when limits are reached:

- **Profit Limit**: Prevents overtrading during winning streaks
- **Loss Limit**: Protects capital during adverse market conditions
- **Automatic Reset**: Counters reset at the start of each trading day

### Maximum Position Limits

- **Single Position**: EA opens only one position at a time by default
- **Daily Trade Count**: Limits the number of trades per day
- **Risk per Trade**: Controls position size based on account balance

### Trading Time Restrictions

- **Session Control**: Trade only during specified hours
- **Weekend Protection**: Automatically avoid weekend gaps
- **Holiday Awareness**: Can be configured to avoid major holidays

### Emergency Controls

- **Spread Monitoring**: Stops trading during high spread conditions
- **Slippage Protection**: Limits acceptable price slippage
- **Friday Close**: Automatically closes positions before weekend

## 🔍 Troubleshooting

### Common Issues and Solutions

#### EA Not Trading
**Symptoms**: EA attached but no trades executed
**Solutions**:
- Check if AutoTrading is enabled (green button in toolbar)
- Verify trading hours settings match your broker's server time
- Ensure minimum account balance requirements are met
- Check spread conditions - may be too high for trading

#### Compilation Errors
**Symptoms**: EA fails to compile in MetaEditor
**Solutions**:
- Ensure TradeUtils.mqh is in the correct Include folder
- Check for syntax errors in custom modifications
- Verify MT5 build version compatibility
- Update MT5 to the latest version

#### Incorrect Position Sizes
**Symptoms**: Lot sizes too large or too small
**Solutions**:
- Check risk management settings
- Verify account balance and leverage
- Ensure lot size parameters are within broker limits
- Review risk percentage calculations

#### No Signal Generation
**Symptoms**: EA running but no trading signals
**Solutions**:
- Check indicator initialization in Experts tab
- Verify sufficient historical data is available
- Review signal conditions - may be too strict
- Check timeframe compatibility with strategy

### Error Codes and Meanings

| Error Code | Description | Solution |
|------------|-------------|----------|
| 4051 | Invalid function parameter | Check input parameters |
| 4106 | Unknown symbol | Verify symbol name and availability |
| 4108 | Invalid ticket | Position may have been closed |
| 4109 | Trading not allowed | Enable AutoTrading |
| 4110 | Long positions not allowed | Check symbol trading permissions |
| 4111 | Short positions not allowed | Check symbol trading permissions |
| 4756 | Invalid stop loss or take profit | Adjust SL/TP levels |

### Performance Optimization Tips

#### Reduce CPU Usage
- Use higher timeframes for signal generation
- Limit the number of indicators
- Optimize indicator periods
- Use efficient coding practices

#### Improve Signal Quality
- Add multiple confirmation indicators
- Implement proper filtering conditions
- Use appropriate timeframes for strategy
- Backtest thoroughly before live trading

#### Memory Management
- Release unused indicator handles
- Limit array sizes
- Clear temporary variables
- Monitor memory usage in Strategy Tester

## 📁 File Structure

```
MQ5EA8/
├── AutoTrader_EA.mq5           # Main Expert Advisor file
├── Include/
│   └── TradeUtils.mqh          # Utility functions library
├── Presets/
│   ├── Conservative.set        # Conservative trading settings
│   └── Aggressive.set          # Aggressive trading settings
├── Documentation/
│   └── Strategy_Guide.md       # Detailed strategy development guide
└── README.md                   # This file
```

### File Descriptions

- **AutoTrader_EA.mq5**: Main EA containing trading logic, risk management, and position handling
- **TradeUtils.mqh**: Reusable utility functions for risk management, market analysis, and position management
- **Conservative.set**: Preset configuration for conservative trading approach
- **Aggressive.set**: Preset configuration for aggressive trading approach
- **Strategy_Guide.md**: Comprehensive guide for developing and customizing trading strategies
- **README.md**: Complete user manual and documentation

## 📞 Support and Resources

### Getting Help
- Review this README thoroughly before seeking support
- Check the Strategy_Guide.md for advanced customization
- Test all changes on demo accounts first
- Keep detailed logs of any issues encountered

### Useful Resources
- MetaTrader 5 Documentation: [MQL5 Reference](https://www.mql5.com/en/docs)
- MQL5 Community: [MQL5.com](https://www.mql5.com)
- Trading Strategy Development: See Documentation/Strategy_Guide.md

### Best Practices
- Always test on demo accounts before live trading
- Keep EA parameters conservative initially
- Monitor performance regularly
- Maintain proper risk management at all times
- Stay updated with market conditions and broker requirements

## ⚠️ Important Disclaimer

### Trading Risks

**RISK WARNING**: Trading foreign exchange (Forex) and Contracts for Difference (CFDs) carries a high level of risk and may not be suitable for all investors. The high degree of leverage can work against you as well as for you. Before deciding to trade foreign exchange or any other financial instrument, you should carefully consider your investment objectives, level of experience, and risk appetite.

### EA Usage Disclaimer

- **No Guarantee of Profits**: This Expert Advisor does not guarantee profitable trading results
- **Past Performance**: Historical performance does not indicate future results
- **Market Conditions**: EA performance may vary significantly under different market conditions
- **User Responsibility**: Users are solely responsible for their trading decisions and outcomes
- **Testing Required**: Thorough testing on demo accounts is essential before live trading
- **Risk Management**: Proper risk management is crucial and user's responsibility

### Technical Disclaimer

- **Software Reliability**: While designed with care, no software is 100% error-free
- **Broker Compatibility**: EA performance may vary between different brokers
- **Market Data**: EA relies on accurate market data from your broker
- **Internet Connection**: Stable internet connection is required for proper operation
- **Regular Monitoring**: Automated trading still requires regular monitoring

### Legal Disclaimer

- **No Financial Advice**: This EA and documentation do not constitute financial advice
- **Regulatory Compliance**: Users must ensure compliance with local financial regulations
- **License Terms**: Use of this EA is subject to the terms specified in the license
- **Liability Limitation**: Developers are not liable for any trading losses or damages

**By using this Expert Advisor, you acknowledge that you have read, understood, and agree to all the terms and disclaimers stated above. Trade responsibly and never risk more than you can afford to lose.**

---

*Copyright 2024, Traycer.AI - Professional Trading Solutions*
*Website: https://www.traycer.ai*