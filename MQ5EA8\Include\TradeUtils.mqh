//+------------------------------------------------------------------+
//|                                                   TradeUtils.mqh |
//|                                    Copyright 2024, Traycer.AI    |
//|                                        https://www.traycer.ai    |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Traycer.AI"
#property link      "https://www.traycer.ai"
#property version   "1.00"
#property description "Trading Utility Functions for MT5 Expert Advisors"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>
#include <Trade\SymbolInfo.mqh>

//+------------------------------------------------------------------+
//| Trading Session Enumeration                                      |
//+------------------------------------------------------------------+
enum ENUM_TRADING_SESSION
{
    SESSION_SYDNEY,     // Sydney session
    SESSION_TOKYO,      // Tokyo session
    SESSION_LONDON,     // London session
    SESSION_NEWYORK,    // New York session
    SESSION_OVERLAP     // Session overlap
};

//+------------------------------------------------------------------+
//| Risk Management Functions                                        |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Calculate position size based on risk percentage                 |
//| Parameters:                                                      |
//|   symbol - Trading symbol                                        |
//|   riskPercent - Risk percentage of account balance               |
//|   stopLossPips - Stop loss in pips                              |
//|   accountBalance - Account balance (0 = use current)            |
//| Returns: Calculated lot size                                     |
//+------------------------------------------------------------------+
double CalculateLotSize(string symbol, double riskPercent, double stopLossPips, double accountBalance = 0)
{
    CSymbolInfo symbolInfo;
    CAccountInfo accountInfo;
    
    if(!symbolInfo.Name(symbol))
    {
        Print("ERROR: Invalid symbol in CalculateLotSize: ", symbol);
        return 0.0;
    }
    
    // Use current balance if not specified
    if(accountBalance <= 0)
        accountBalance = accountInfo.Balance();
    
    // Calculate risk amount
    double riskAmount = accountBalance * riskPercent / 100.0;
    
    // Get symbol properties
    double tickValue = symbolInfo.TickValue();
    double tickSize = symbolInfo.TickSize();
    double point = symbolInfo.Point();
    
    if(tickValue <= 0 || tickSize <= 0 || point <= 0)
    {
        Print("ERROR: Invalid symbol properties in CalculateLotSize");
        return 0.0;
    }
    
    // Calculate lot size
    double stopLossValue = stopLossPips * point;
    double lotSize = 0.0;
    
    if(stopLossValue > 0)
    {
        lotSize = riskAmount / (stopLossValue / tickSize * tickValue);
    }
    
    // Normalize lot size to symbol requirements
    double minLot = symbolInfo.LotsMin();
    double maxLot = symbolInfo.LotsMax();
    double lotStep = symbolInfo.LotsStep();
    
    lotSize = MathMax(lotSize, minLot);
    lotSize = MathMin(lotSize, maxLot);
    lotSize = NormalizeDouble(MathRound(lotSize / lotStep) * lotStep, 2);
    
    return lotSize;
}

//+------------------------------------------------------------------+
//| Validate Stop Loss and Take Profit levels                       |
//| Parameters:                                                      |
//|   symbol - Trading symbol                                        |
//|   orderType - Order type (buy/sell)                             |
//|   price - Entry price                                            |
//|   stopLoss - Stop loss price                                     |
//|   takeProfit - Take profit price                                 |
//| Returns: true if levels are valid                                |
//+------------------------------------------------------------------+
bool ValidateStopLoss(string symbol, ENUM_ORDER_TYPE orderType, double price, double stopLoss, double takeProfit)
{
    CSymbolInfo symbolInfo;
    
    if(!symbolInfo.Name(symbol))
    {
        Print("ERROR: Invalid symbol in ValidateStopLoss: ", symbol);
        return false;
    }
    
    // Get symbol properties
    int stopsLevel = (int)symbolInfo.StopsLevel();
    double point = symbolInfo.Point();
    double minDistance = stopsLevel * point;
    
    // Validate stop loss
    if(stopLoss > 0)
    {
        double slDistance = 0;
        if(orderType == ORDER_TYPE_BUY)
            slDistance = price - stopLoss;
        else if(orderType == ORDER_TYPE_SELL)
            slDistance = stopLoss - price;
        
        if(slDistance < minDistance)
        {
            Print("ERROR: Stop loss too close to market price. Minimum distance: ", minDistance / point, " points");
            return false;
        }
    }
    
    // Validate take profit
    if(takeProfit > 0)
    {
        double tpDistance = 0;
        if(orderType == ORDER_TYPE_BUY)
            tpDistance = takeProfit - price;
        else if(orderType == ORDER_TYPE_SELL)
            tpDistance = price - takeProfit;
        
        if(tpDistance < minDistance)
        {
            Print("ERROR: Take profit too close to market price. Minimum distance: ", minDistance / point, " points");
            return false;
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Calculate maximum risk per trade                                 |
//| Parameters:                                                      |
//|   riskPercent - Risk percentage of account                       |
//|   accountBalance - Account balance (0 = use current)            |
//| Returns: Maximum risk amount in account currency                 |
//+------------------------------------------------------------------+
double GetAccountRisk(double riskPercent, double accountBalance = 0)
{
    CAccountInfo accountInfo;
    
    if(accountBalance <= 0)
        accountBalance = accountInfo.Balance();
    
    if(riskPercent <= 0 || riskPercent > 100)
    {
        Print("ERROR: Invalid risk percentage: ", riskPercent);
        return 0.0;
    }
    
    double riskAmount = accountBalance * riskPercent / 100.0;
    
    // Additional safety check - limit maximum risk
    double maxRisk = accountBalance * 0.1; // Maximum 10% risk
    riskAmount = MathMin(riskAmount, maxRisk);
    
    return riskAmount;
}

//+------------------------------------------------------------------+
//| Market Analysis Functions                                        |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Check if market is open for trading                             |
//| Parameters:                                                      |
//|   symbol - Trading symbol                                        |
//| Returns: true if market is open                                  |
//+------------------------------------------------------------------+
bool IsMarketOpen(string symbol)
{
    CSymbolInfo symbolInfo;
    
    if(!symbolInfo.Name(symbol))
    {
        Print("ERROR: Invalid symbol in IsMarketOpen: ", symbol);
        return false;
    }
    
    // Check if symbol is available for trading
    if(!symbolInfo.Select())
    {
        Print("WARNING: Symbol not selected in Market Watch: ", symbol);
        return false;
    }
    
    // Check trading session
    MqlDateTime timeStruct;
    TimeToStruct(TimeCurrent(), timeStruct);
    
    // Basic market hours check (Monday to Friday)
    if(timeStruct.day_of_week == 0 || timeStruct.day_of_week == 6)
        return false;
    
    // Check if trading is allowed
    if(!symbolInfo.IsTradingAllowed())
        return false;
    
    // Check session times
    datetime sessionBegin, sessionEnd;
    if(!symbolInfo.SessionTrade(timeStruct.day_of_week, 0, sessionBegin, sessionEnd))
        return false;
    
    datetime currentTime = TimeCurrent() % 86400; // Seconds since midnight
    sessionBegin = sessionBegin % 86400;
    sessionEnd = sessionEnd % 86400;
    
    if(sessionBegin <= sessionEnd)
        return (currentTime >= sessionBegin && currentTime <= sessionEnd);
    else
        return (currentTime >= sessionBegin || currentTime <= sessionEnd);
}

//+------------------------------------------------------------------+
//| Get current spread information                                   |
//| Parameters:                                                      |
//|   symbol - Trading symbol                                        |
//| Returns: Spread in pips                                          |
//+------------------------------------------------------------------+
double GetSpread(string symbol)
{
    CSymbolInfo symbolInfo;
    
    if(!symbolInfo.Name(symbol))
    {
        Print("ERROR: Invalid symbol in GetSpread: ", symbol);
        return -1.0;
    }
    
    double spread = symbolInfo.Spread() * symbolInfo.Point();
    double pipValue = symbolInfo.Point();
    
    // Convert to pips for 5-digit brokers
    if(symbolInfo.Digits() == 5 || symbolInfo.Digits() == 3)
        pipValue *= 10;
    
    return spread / pipValue;
}

//+------------------------------------------------------------------+
//| Basic volatility assessment using ATR                           |
//| Parameters:                                                      |
//|   symbol - Trading symbol                                        |
//|   period - Timeframe for calculation                            |
//|   atrPeriod - ATR period                                         |
//| Returns: ATR value in pips                                       |
//+------------------------------------------------------------------+
double CheckVolatility(string symbol, ENUM_TIMEFRAMES period = PERIOD_H1, int atrPeriod = 14)
{
    int atrHandle = iATR(symbol, period, atrPeriod);
    
    if(atrHandle == INVALID_HANDLE)
    {
        Print("ERROR: Failed to create ATR indicator for volatility check");
        return -1.0;
    }
    
    double atrBuffer[1];
    if(CopyBuffer(atrHandle, 0, 0, 1, atrBuffer) <= 0)
    {
        Print("ERROR: Failed to copy ATR buffer");
        IndicatorRelease(atrHandle);
        return -1.0;
    }
    
    IndicatorRelease(atrHandle);
    
    CSymbolInfo symbolInfo;
    symbolInfo.Name(symbol);
    
    double pipValue = symbolInfo.Point();
    if(symbolInfo.Digits() == 5 || symbolInfo.Digits() == 3)
        pipValue *= 10;
    
    return atrBuffer[0] / pipValue;
}

//+------------------------------------------------------------------+
//| Position Management Functions                                    |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Count open positions by magic number                            |
//| Parameters:                                                      |
//|   symbol - Trading symbol (empty = all symbols)                 |
//|   magicNumber - Magic number (0 = all magic numbers)           |
//| Returns: Number of open positions                                |
//+------------------------------------------------------------------+
int CountOpenPositions(string symbol = "", int magicNumber = 0)
{
    CPositionInfo position;
    int count = 0;
    
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(!position.SelectByIndex(i))
            continue;
        
        // Check symbol filter
        if(symbol != "" && position.Symbol() != symbol)
            continue;
        
        // Check magic number filter
        if(magicNumber != 0 && position.Magic() != magicNumber)
            continue;
        
        count++;
    }
    
    return count;
}

//+------------------------------------------------------------------+
//| Calculate total profit/loss from positions                      |
//| Parameters:                                                      |
//|   symbol - Trading symbol (empty = all symbols)                 |
//|   magicNumber - Magic number (0 = all magic numbers)           |
//| Returns: Total profit/loss                                       |
//+------------------------------------------------------------------+
double GetPositionProfit(string symbol = "", int magicNumber = 0)
{
    CPositionInfo position;
    double totalProfit = 0.0;
    
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(!position.SelectByIndex(i))
            continue;
        
        // Check symbol filter
        if(symbol != "" && position.Symbol() != symbol)
            continue;
        
        // Check magic number filter
        if(magicNumber != 0 && position.Magic() != magicNumber)
            continue;
        
        totalProfit += position.Profit() + position.Swap() + position.Commission();
    }
    
    return totalProfit;
}

//+------------------------------------------------------------------+
//| Emergency close all positions                                   |
//| Parameters:                                                      |
//|   symbol - Trading symbol (empty = all symbols)                 |
//|   magicNumber - Magic number (0 = all magic numbers)           |
//| Returns: Number of positions closed                              |
//+------------------------------------------------------------------+
int CloseAllPositions(string symbol = "", int magicNumber = 0)
{
    CTrade trade;
    CPositionInfo position;
    int closedCount = 0;
    
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(!position.SelectByIndex(i))
            continue;
        
        // Check symbol filter
        if(symbol != "" && position.Symbol() != symbol)
            continue;
        
        // Check magic number filter
        if(magicNumber != 0 && position.Magic() != magicNumber)
            continue;
        
        if(trade.PositionClose(position.Ticket()))
        {
            closedCount++;
            Print("Position closed: Ticket ", position.Ticket(), " Symbol: ", position.Symbol());
        }
        else
        {
            Print("ERROR: Failed to close position ", position.Ticket(), " Error: ", trade.ResultRetcode());
        }
    }
    
    return closedCount;
}

//+------------------------------------------------------------------+
//| Time and Session Functions                                       |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Check if current time is within trading hours                   |
//| Parameters:                                                      |
//|   startHour - Start hour (0-23)                                 |
//|   endHour - End hour (0-23)                                     |
//|   mondayToFriday - Trade only Monday to Friday                  |
//| Returns: true if within trading hours                           |
//+------------------------------------------------------------------+
bool IsWithinTradingHours(int startHour, int endHour, bool mondayToFriday = true)
{
    MqlDateTime timeStruct;
    TimeToStruct(TimeCurrent(), timeStruct);
    
    // Check day of week
    if(mondayToFriday && (timeStruct.day_of_week == 0 || timeStruct.day_of_week == 6))
        return false;
    
    // Validate hour parameters
    if(startHour < 0 || startHour > 23 || endHour < 0 || endHour > 23)
    {
        Print("ERROR: Invalid trading hours in IsWithinTradingHours");
        return false;
    }
    
    int currentHour = timeStruct.hour;
    
    // Handle same day trading hours
    if(startHour <= endHour)
    {
        return (currentHour >= startHour && currentHour < endHour);
    }
    // Handle overnight trading hours
    else
    {
        return (currentHour >= startHour || currentHour < endHour);
    }
}

//+------------------------------------------------------------------+
//| Get trading session information                                 |
//| Parameters:                                                      |
//|   currentTime - Time to check (0 = current time)               |
//| Returns: Current trading session                                 |
//+------------------------------------------------------------------+
ENUM_TRADING_SESSION GetSessionInfo(datetime currentTime = 0)
{
    if(currentTime == 0)
        currentTime = TimeCurrent();
    
    MqlDateTime timeStruct;
    TimeToStruct(currentTime, timeStruct);
    
    int hour = timeStruct.hour;
    
    // Convert to GMT/UTC hours for session detection
    // Note: This is a simplified version - real implementation should consider DST
    
    // Sydney: 22:00 - 07:00 GMT
    if((hour >= 22) || (hour < 7))
        return SESSION_SYDNEY;
    
    // Tokyo: 00:00 - 09:00 GMT
    if(hour >= 0 && hour < 9)
        return SESSION_TOKYO;
    
    // London: 08:00 - 17:00 GMT
    if(hour >= 8 && hour < 17)
        return SESSION_LONDON;
    
    // New York: 13:00 - 22:00 GMT
    if(hour >= 13 && hour < 22)
        return SESSION_NEWYORK;
    
    // Check for overlaps
    if((hour >= 8 && hour < 9) || (hour >= 13 && hour < 17))
        return SESSION_OVERLAP;
    
    return SESSION_SYDNEY; // Default
}

//+------------------------------------------------------------------+
//| Utility Functions                                               |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Normalize price to symbol digits                                |
//| Parameters:                                                      |
//|   symbol - Trading symbol                                        |
//|   price - Price to normalize                                     |
//| Returns: Normalized price                                        |
//+------------------------------------------------------------------+
double NormalizePrice(string symbol, double price)
{
    CSymbolInfo symbolInfo;
    
    if(!symbolInfo.Name(symbol))
    {
        Print("ERROR: Invalid symbol in NormalizePrice: ", symbol);
        return price;
    }
    
    return symbolInfo.NormalizePrice(price);
}

//+------------------------------------------------------------------+
//| Enhanced logging with timestamps                                |
//| Parameters:                                                      |
//|   message - Message to log                                       |
//|   logLevel - Log level (INFO, WARNING, ERROR)                   |
//|   includeTime - Include timestamp                               |
//+------------------------------------------------------------------+
void LogMessage(string message, string logLevel = "INFO", bool includeTime = true)
{
    string logEntry = "";
    
    if(includeTime)
    {
        logEntry += TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS);
        logEntry += " ";
    }
    
    logEntry += "[" + logLevel + "] ";
    logEntry += message;
    
    Print(logEntry);
    
    // Optional: Write to file for persistent logging
    // FileWrite(logFileHandle, logEntry);
}

//+------------------------------------------------------------------+
//| Send alerts/notifications                                       |
//| Parameters:                                                      |
//|   message - Notification message                                |
//|   sendAlert - Send terminal alert                               |
//|   sendNotification - Send push notification                     |
//|   sendEmail - Send email notification                           |
//| Returns: true if at least one notification was sent             |
//+------------------------------------------------------------------+
bool SendNotification(string message, bool sendAlert = true, bool sendNotification = false, bool sendEmail = false)
{
    bool result = false;
    string fullMessage = TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + " - " + message;
    
    // Send terminal alert
    if(sendAlert)
    {
        Alert(fullMessage);
        result = true;
    }
    
    // Send push notification to mobile
    if(sendNotification && TerminalInfoInteger(TERMINAL_NOTIFICATIONS_ENABLED))
    {
        if(SendNotification(fullMessage))
        {
            result = true;
            LogMessage("Push notification sent: " + message);
        }
        else
        {
            LogMessage("Failed to send push notification", "WARNING");
        }
    }
    
    // Send email notification
    if(sendEmail && TerminalInfoInteger(TERMINAL_EMAIL_ENABLED))
    {
        string subject = "MT5 EA Notification - " + Symbol();
        if(SendMail(subject, fullMessage))
        {
            result = true;
            LogMessage("Email notification sent: " + message);
        }
        else
        {
            LogMessage("Failed to send email notification", "WARNING");
        }
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| Additional Utility Functions                                    |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Convert pips to points for the symbol                           |
//| Parameters:                                                      |
//|   symbol - Trading symbol                                        |
//|   pips - Value in pips                                          |
//| Returns: Value in points                                         |
//+------------------------------------------------------------------+
double PipsToPoints(string symbol, double pips)
{
    CSymbolInfo symbolInfo;
    
    if(!symbolInfo.Name(symbol))
    {
        Print("ERROR: Invalid symbol in PipsToPoints: ", symbol);
        return pips;
    }
    
    double point = symbolInfo.Point();
    
    // For 5-digit and 3-digit brokers, 1 pip = 10 points
    if(symbolInfo.Digits() == 5 || symbolInfo.Digits() == 3)
        return pips * 10 * point;
    else
        return pips * point;
}

//+------------------------------------------------------------------+
//| Convert points to pips for the symbol                           |
//| Parameters:                                                      |
//|   symbol - Trading symbol                                        |
//|   points - Value in points                                       |
//| Returns: Value in pips                                           |
//+------------------------------------------------------------------+
double PointsToPips(string symbol, double points)
{
    CSymbolInfo symbolInfo;
    
    if(!symbolInfo.Name(symbol))
    {
        Print("ERROR: Invalid symbol in PointsToPips: ", symbol);
        return points;
    }
    
    double point = symbolInfo.Point();
    
    // For 5-digit and 3-digit brokers, 1 pip = 10 points
    if(symbolInfo.Digits() == 5 || symbolInfo.Digits() == 3)
        return (points / point) / 10;
    else
        return points / point;
}

//+------------------------------------------------------------------+
//| Check if it's a new bar                                         |
//| Parameters:                                                      |
//|   symbol - Trading symbol                                        |
//|   timeframe - Chart timeframe                                    |
//|   lastBarTime - Reference to last bar time variable             |
//| Returns: true if new bar formed                                  |
//+------------------------------------------------------------------+
bool IsNewBar(string symbol, ENUM_TIMEFRAMES timeframe, datetime &lastBarTime)
{
    datetime currentBarTime = iTime(symbol, timeframe, 0);
    
    if(currentBarTime != lastBarTime)
    {
        lastBarTime = currentBarTime;
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Get account information summary                                  |
//| Returns: Formatted string with account details                  |
//+------------------------------------------------------------------+
string GetAccountSummary()
{
    CAccountInfo account;
    
    string summary = "";
    summary += "Account: " + IntegerToString(account.Login()) + "\n";
    summary += "Balance: " + DoubleToString(account.Balance(), 2) + " " + account.Currency() + "\n";
    summary += "Equity: " + DoubleToString(account.Equity(), 2) + " " + account.Currency() + "\n";
    summary += "Free Margin: " + DoubleToString(account.FreeMargin(), 2) + " " + account.Currency() + "\n";
    summary += "Margin Level: " + DoubleToString(account.MarginLevel(), 2) + "%\n";
    summary += "Leverage: 1:" + IntegerToString(account.Leverage());
    
    return summary;
}