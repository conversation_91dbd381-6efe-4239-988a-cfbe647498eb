//+------------------------------------------------------------------+
//|                                                AutoTrader_EA.mq5 |
//|                                    Copyright 2024, Traycer.AI    |
//|                                        https://www.traycer.ai    |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Traycer.AI"
#property link      "https://www.traycer.ai"
#property version   "1.00"
#property description "Professional MT5 Expert Advisor Template"
#property description "Customizable automated trading system with risk management"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>
#include <Trade\SymbolInfo.mqh>

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== Trading Parameters ==="
input double   InpLotSize = 0.1;                    // Lot Size
input int      InpStopLoss = 50;                    // Stop Loss (pips)
input int      InpTakeProfit = 100;                 // Take Profit (pips)
input int      InpMagicNumber = 12345;              // Magic Number
input bool     InpUseRiskManagement = true;         // Use Risk Management
input double   InpRiskPercent = 2.0;                // Risk Percent per Trade

input group "=== Trading Hours ==="
input int      InpStartHour = 8;                    // Start Trading Hour (0-23)
input int      InpEndHour = 18;                     // End Trading Hour (0-23)
input bool     InpTradeMondayToFriday = true;       // Trade Monday to Friday Only

input group "=== Daily Limits ==="
input bool     InpUseDailyLimits = true;            // Use Daily Limits
input double   InpDailyProfitLimit = 100.0;         // Daily Profit Limit ($)
input double   InpDailyLossLimit = 50.0;            // Daily Loss Limit ($)
input int      InpMaxDailyTrades = 10;              // Maximum Daily Trades

input group "=== Strategy Settings ==="
input int      InpFastMA = 10;                      // Fast Moving Average Period
input int      InpSlowMA = 20;                      // Slow Moving Average Period
input ENUM_MA_METHOD InpMAMethod = MODE_EMA;        // Moving Average Method
input ENUM_APPLIED_PRICE InpMAPrice = PRICE_CLOSE;  // Applied Price

input group "=== Risk Controls ==="
input double   InpMaxSpread = 3.0;                  // Maximum Spread (pips)
input int      InpSlippage = 3;                     // Maximum Slippage (pips)
input bool     InpCloseOnFriday = true;             // Close All Positions on Friday

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
CTrade         trade;                               // Trade execution object
CPositionInfo  position;                            // Position information object
CAccountInfo   account;                             // Account information object
CSymbolInfo    symbol;                              // Symbol information object

// Trading variables
datetime       g_lastBarTime = 0;                  // Last processed bar time
int            g_dailyTradeCount = 0;               // Daily trade counter
double         g_dailyProfit = 0.0;                 // Daily profit/loss
datetime       g_lastDayCheck = 0;                  // Last day checked for daily reset
bool           g_tradingAllowed = true;             // Trading permission flag

// Indicator handles
int            g_fastMAHandle = INVALID_HANDLE;     // Fast MA indicator handle
int            g_slowMAHandle = INVALID_HANDLE;     // Slow MA indicator handle

// Arrays for indicator values
double         g_fastMABuffer[];                    // Fast MA values
double         g_slowMABuffer[];                    // Slow MA values

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize trade object
    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(Symbol());
    trade.SetDeviationInPoints(InpSlippage);
    
    // Initialize symbol object
    if(!symbol.Name(Symbol()))
    {
        Print("ERROR: Failed to initialize symbol object");
        return INIT_FAILED;
    }
    
    // Validate input parameters
    if(!ValidateInputParameters())
    {
        Print("ERROR: Invalid input parameters");
        return INIT_FAILED;
    }
    
    // Initialize indicators
    if(!InitializeIndicators())
    {
        Print("ERROR: Failed to initialize indicators");
        return INIT_FAILED;
    }
    
    // Set array properties
    ArraySetAsSeries(g_fastMABuffer, true);
    ArraySetAsSeries(g_slowMABuffer, true);
    
    // Initialize daily tracking
    ResetDailyCounters();
    
    // Print initialization message
    Print("AutoTrader EA initialized successfully");
    Print("Symbol: ", Symbol());
    Print("Magic Number: ", InpMagicNumber);
    Print("Lot Size: ", InpLotSize);
    Print("Risk Management: ", InpUseRiskManagement ? "Enabled" : "Disabled");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    if(g_fastMAHandle != INVALID_HANDLE)
        IndicatorRelease(g_fastMAHandle);
    if(g_slowMAHandle != INVALID_HANDLE)
        IndicatorRelease(g_slowMAHandle);
    
    // Print deinitialization message
    string reasonText = "";
    switch(reason)
    {
        case REASON_PROGRAM: reasonText = "EA stopped by user"; break;
        case REASON_REMOVE: reasonText = "EA removed from chart"; break;
        case REASON_RECOMPILE: reasonText = "EA recompiled"; break;
        case REASON_CHARTCHANGE: reasonText = "Chart symbol/period changed"; break;
        case REASON_CHARTCLOSE: reasonText = "Chart closed"; break;
        case REASON_PARAMETERS: reasonText = "Input parameters changed"; break;
        case REASON_ACCOUNT: reasonText = "Account changed"; break;
        default: reasonText = "Unknown reason"; break;
    }
    
    Print("AutoTrader EA deinitialized. Reason: ", reasonText);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new bar formed
    if(!IsNewBar())
        return;
    
    // Update daily counters if new day
    CheckAndResetDailyCounters();
    
    // Check if trading is allowed
    if(!IsTradingAllowed())
        return;
    
    // Update indicator values
    if(!UpdateIndicatorValues())
    {
        Print("ERROR: Failed to update indicator values");
        return;
    }
    
    // Check market conditions
    if(!CheckMarketConditions())
        return;
    
    // Main trading logic
    ProcessTradingSignals();
    
    // Manage existing positions
    ManageOpenPositions();
    
    // Check daily limits
    if(InpUseDailyLimits)
        CheckDailyLimits();
}

//+------------------------------------------------------------------+
//| Validate input parameters                                        |
//+------------------------------------------------------------------+
bool ValidateInputParameters()
{
    // Validate lot size
    double minLot = symbol.LotsMin();
    double maxLot = symbol.LotsMax();
    double lotStep = symbol.LotsStep();
    
    if(InpLotSize < minLot || InpLotSize > maxLot)
    {
        Print("ERROR: Invalid lot size. Min: ", minLot, ", Max: ", maxLot);
        return false;
    }
    
    // Validate stop loss and take profit
    if(InpStopLoss <= 0 || InpTakeProfit <= 0)
    {
        Print("ERROR: Stop Loss and Take Profit must be positive");
        return false;
    }
    
    // Validate trading hours
    if(InpStartHour < 0 || InpStartHour > 23 || InpEndHour < 0 || InpEndHour > 23)
    {
        Print("ERROR: Invalid trading hours");
        return false;
    }
    
    // Validate risk percentage
    if(InpUseRiskManagement && (InpRiskPercent <= 0 || InpRiskPercent > 10))
    {
        Print("ERROR: Risk percentage must be between 0 and 10");
        return false;
    }
    
    // Validate MA periods
    if(InpFastMA <= 0 || InpSlowMA <= 0 || InpFastMA >= InpSlowMA)
    {
        Print("ERROR: Invalid MA periods. Fast MA must be less than Slow MA");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Initialize indicators                                             |
//+------------------------------------------------------------------+
bool InitializeIndicators()
{
    // Create Fast MA indicator
    g_fastMAHandle = iMA(Symbol(), PERIOD_CURRENT, InpFastMA, 0, InpMAMethod, InpMAPrice);
    if(g_fastMAHandle == INVALID_HANDLE)
    {
        Print("ERROR: Failed to create Fast MA indicator");
        return false;
    }
    
    // Create Slow MA indicator
    g_slowMAHandle = iMA(Symbol(), PERIOD_CURRENT, InpSlowMA, 0, InpMAMethod, InpMAPrice);
    if(g_slowMAHandle == INVALID_HANDLE)
    {
        Print("ERROR: Failed to create Slow MA indicator");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime currentBarTime = iTime(Symbol(), PERIOD_CURRENT, 0);
    if(currentBarTime != g_lastBarTime)
    {
        g_lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check and reset daily counters                                   |
//+------------------------------------------------------------------+
void CheckAndResetDailyCounters()
{
    datetime currentDay = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    if(currentDay != g_lastDayCheck)
    {
        ResetDailyCounters();
        g_lastDayCheck = currentDay;
    }
}

//+------------------------------------------------------------------+
//| Reset daily counters                                             |
//+------------------------------------------------------------------+
void ResetDailyCounters()
{
    g_dailyTradeCount = 0;
    g_dailyProfit = CalculateDailyProfit();
    g_lastDayCheck = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                      |
//+------------------------------------------------------------------+
bool IsTradingAllowed()
{
    // Check if trading is globally allowed
    if(!g_tradingAllowed)
        return false;
    
    // Check if auto trading is enabled
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
    {
        Print("WARNING: Auto trading is disabled in terminal");
        return false;
    }
    
    // Check if trading is allowed for this symbol
    if(!symbol.IsTradingAllowed())
    {
        Print("WARNING: Trading is not allowed for ", Symbol());
        return false;
    }
    
    // Check trading hours
    if(!IsWithinTradingHours())
        return false;
    
    // Check daily trade limit
    if(InpUseDailyLimits && g_dailyTradeCount >= InpMaxDailyTrades)
    {
        Print("INFO: Daily trade limit reached");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if current time is within trading hours                    |
//+------------------------------------------------------------------+
bool IsWithinTradingHours()
{
    MqlDateTime timeStruct;
    TimeToStruct(TimeCurrent(), timeStruct);
    
    // Check day of week
    if(InpTradeMondayToFriday && (timeStruct.day_of_week == 0 || timeStruct.day_of_week == 6))
        return false;
    
    // Check trading hours
    int currentHour = timeStruct.hour;
    if(InpStartHour <= InpEndHour)
    {
        return (currentHour >= InpStartHour && currentHour < InpEndHour);
    }
    else
    {
        return (currentHour >= InpStartHour || currentHour < InpEndHour);
    }
}

//+------------------------------------------------------------------+
//| Update indicator values                                           |
//+------------------------------------------------------------------+
bool UpdateIndicatorValues()
{
    // Get Fast MA values
    if(CopyBuffer(g_fastMAHandle, 0, 0, 3, g_fastMABuffer) < 3)
    {
        Print("ERROR: Failed to copy Fast MA buffer");
        return false;
    }
    
    // Get Slow MA values
    if(CopyBuffer(g_slowMAHandle, 0, 0, 3, g_slowMABuffer) < 3)
    {
        Print("ERROR: Failed to copy Slow MA buffer");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check market conditions                                          |
//+------------------------------------------------------------------+
bool CheckMarketConditions()
{
    // Check spread
    double spread = symbol.Spread() * symbol.Point() / symbol.Point();
    if(spread > InpMaxSpread)
    {
        Print("INFO: Spread too high: ", spread, " pips");
        return false;
    }
    
    // Check if market is open
    if(!symbol.IsTradingAllowed())
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Process trading signals                                          |
//+------------------------------------------------------------------+
void ProcessTradingSignals()
{
    // Check if we already have open positions
    if(CountOpenPositions() > 0)
        return;
    
    // Get trading signal
    ENUM_ORDER_TYPE signal = GetTradingSignal();
    
    if(signal == ORDER_TYPE_BUY)
    {
        OpenPosition(ORDER_TYPE_BUY);
    }
    else if(signal == ORDER_TYPE_SELL)
    {
        OpenPosition(ORDER_TYPE_SELL);
    }
}

//+------------------------------------------------------------------+
//| Get trading signal based on MA crossover                         |
//+------------------------------------------------------------------+
ENUM_ORDER_TYPE GetTradingSignal()
{
    // Simple MA crossover strategy
    double fastMA_current = g_fastMABuffer[0];
    double fastMA_previous = g_fastMABuffer[1];
    double slowMA_current = g_slowMABuffer[0];
    double slowMA_previous = g_slowMABuffer[1];
    
    // Bullish crossover - Fast MA crosses above Slow MA
    if(fastMA_previous <= slowMA_previous && fastMA_current > slowMA_current)
    {
        return ORDER_TYPE_BUY;
    }
    
    // Bearish crossover - Fast MA crosses below Slow MA
    if(fastMA_previous >= slowMA_previous && fastMA_current < slowMA_current)
    {
        return ORDER_TYPE_SELL;
    }
    
    return -1; // No signal
}

//+------------------------------------------------------------------+
//| Open position                                                    |
//+------------------------------------------------------------------+
void OpenPosition(ENUM_ORDER_TYPE orderType)
{
    double lotSize = CalculateLotSize();
    double price = 0;
    double sl = 0;
    double tp = 0;
    
    if(orderType == ORDER_TYPE_BUY)
    {
        price = symbol.Ask();
        sl = price - InpStopLoss * symbol.Point();
        tp = price + InpTakeProfit * symbol.Point();
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        price = symbol.Bid();
        sl = price + InpStopLoss * symbol.Point();
        tp = price - InpTakeProfit * symbol.Point();
    }
    
    // Normalize prices
    price = symbol.NormalizePrice(price);
    sl = symbol.NormalizePrice(sl);
    tp = symbol.NormalizePrice(tp);
    
    // Execute trade
    bool result = false;
    if(orderType == ORDER_TYPE_BUY)
    {
        result = trade.Buy(lotSize, Symbol(), price, sl, tp, "AutoTrader EA");
    }
    else
    {
        result = trade.Sell(lotSize, Symbol(), price, sl, tp, "AutoTrader EA");
    }
    
    if(result)
    {
        g_dailyTradeCount++;
        Print("Position opened: ", EnumToString(orderType), " ", lotSize, " lots at ", price);
    }
    else
    {
        Print("ERROR: Failed to open position. Error: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk management                      |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
    double lotSize = InpLotSize;
    
    if(InpUseRiskManagement)
    {
        double balance = account.Balance();
        double riskAmount = balance * InpRiskPercent / 100.0;
        double tickValue = symbol.TickValue();
        double stopLossPoints = InpStopLoss * symbol.Point();
        
        if(tickValue > 0 && stopLossPoints > 0)
        {
            lotSize = riskAmount / (stopLossPoints / symbol.Point() * tickValue);
        }
    }
    
    // Normalize lot size
    double minLot = symbol.LotsMin();
    double maxLot = symbol.LotsMax();
    double lotStep = symbol.LotsStep();
    
    lotSize = MathMax(lotSize, minLot);
    lotSize = MathMin(lotSize, maxLot);
    lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;
    
    return lotSize;
}

//+------------------------------------------------------------------+
//| Count open positions                                             |
//+------------------------------------------------------------------+
int CountOpenPositions()
{
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Magic() == InpMagicNumber)
            {
                count++;
            }
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Manage open positions                                            |
//+------------------------------------------------------------------+
void ManageOpenPositions()
{
    // Close positions on Friday if enabled
    if(InpCloseOnFriday)
    {
        MqlDateTime timeStruct;
        TimeToStruct(TimeCurrent(), timeStruct);
        if(timeStruct.day_of_week == 5 && timeStruct.hour >= 20) // Friday after 8 PM
        {
            CloseAllPositions();
        }
    }
    
    // Additional position management logic can be added here
    // For example: trailing stops, partial closes, etc.
}

//+------------------------------------------------------------------+
//| Close all positions                                              |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Magic() == InpMagicNumber)
            {
                trade.PositionClose(position.Ticket());
                Print("Position closed: Ticket ", position.Ticket());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate daily profit                                           |
//+------------------------------------------------------------------+
double CalculateDailyProfit()
{
    double profit = 0.0;
    datetime startOfDay = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    
    // Calculate profit from closed trades today
    HistorySelect(startOfDay, TimeCurrent());
    for(int i = 0; i < HistoryDealsTotal(); i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(HistoryDealGetString(ticket, DEAL_SYMBOL) == Symbol() &&
           HistoryDealGetInteger(ticket, DEAL_MAGIC) == InpMagicNumber &&
           HistoryDealGetInteger(ticket, DEAL_TYPE) <= DEAL_TYPE_SELL)
        {
            profit += HistoryDealGetDouble(ticket, DEAL_PROFIT);
        }
    }
    
    // Add profit from open positions
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Magic() == InpMagicNumber)
            {
                profit += position.Profit();
            }
        }
    }
    
    return profit;
}

//+------------------------------------------------------------------+
//| Check daily limits                                               |
//+------------------------------------------------------------------+
void CheckDailyLimits()
{
    double currentDailyProfit = CalculateDailyProfit();
    
    // Check daily profit limit
    if(currentDailyProfit >= InpDailyProfitLimit)
    {
        Print("Daily profit limit reached: $", currentDailyProfit);
        CloseAllPositions();
        g_tradingAllowed = false;
    }
    
    // Check daily loss limit
    if(currentDailyProfit <= -InpDailyLossLimit)
    {
        Print("Daily loss limit reached: $", currentDailyProfit);
        CloseAllPositions();
        g_tradingAllowed = false;
    }
}

//+------------------------------------------------------------------+
//| Log message with timestamp                                       |
//+------------------------------------------------------------------+
void LogMessage(string message)
{
    Print(TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS), " - ", message);
}