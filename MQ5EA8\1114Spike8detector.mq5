//+------------------------------------------------------------------+
//|                    V75_1s_SpikeHunter_Improved.mq5             |
//|                   Improved EA with Better Signal Quality        |
//|                   Focus on Quality over Quantity                |
//+------------------------------------------------------------------+
#property copyright "2025"
#property version   "6.0"
#property strict

// === IMPROVED SIGNAL FILTERING ===
input group "=== ENHANCED SIGNAL QUALITY ==="
input bool     UseAdvancedFiltering = true;
input double   MinSpikeSize = 80.0;         // Increased minimum spike size
input double   MaxSpikeSize = 800.0;        // Avoid extreme volatile spikes
input double   MinBodyRatio = 0.45;         // Higher body ratio for stronger signals
input double   MaxBodyRatio = 0.85;         // Avoid doji-like candles
input double   MinATRMultiplier = 1.5;      // Stronger ATR requirement
input double   MaxATRMultiplier = 4.0;      // Avoid extreme outliers

input group "=== CONFLUENCE FILTERS ==="
input bool     UseVolumeFilter = true;      // Volume confirmation
input bool     UseVolatilityFilter = true;  // ATR-based volatility filter
input bool     UseMomentumFilter = true;    // RSI momentum filter
input bool     UseStructureFilter = true;   // Support/Resistance levels
input int      RSI_Period = 14;
input double   RSI_Oversold = 30.0;
input double   RSI_Overbought = 70.0;

input group "=== MARKET SESSION FILTER ==="
input bool     UseSessionFilter = true;
input int      StartHour = 8;               // London session start
input int      EndHour = 16;                // Overlap with NY session
input bool     AvoidNews = true;            // Avoid high-impact news times

input group "=== IMPROVED RISK MANAGEMENT ==="
input double   RiskPercent = 0.3;           // Reduced risk per trade
input double   MaxDailyRisk = 2.0;          // Maximum daily risk %
input double   FixedLot = 0.05;             // Smaller fixed lot
input bool     UseFixedLot = true;
input double   MaxLot = 0.5;                // Reduced max lot
input double   RRRatio = 2.5;               // Better risk-reward
input double   BreakevenPips = 15.0;        // Move to breakeven faster
input double   TrailStartPips = 25.0;       // Start trailing after profit
input double   TrailPips = 8.0;

input group "=== TRADE TIMING ==="
input int      MinutesAfterSpike = 2;       // Wait for confirmation
input int      CooldownMinutes = 30;        // Longer cooldown between trades
input int      MaxTradesPerDay = 8;         // Limit daily trades
input int      MaxConsecutiveLosses = 3;    // Stop after consecutive losses

input group "=== MARKET CONDITIONS ==="
input bool     AvoidLowVolatility = true;
input bool     AvoidHighVolatility = true;
input double   MinDailyATR = 200.0;         // Minimum daily volatility
input double   MaxDailyATR = 1000.0;        // Maximum daily volatility

input group "=== DEBUG AND TRACKING ==="
input bool     ShowDebug = false;           // Reduced debug output
input bool     TrackDetailedStats = true;
input bool     EnableOptimizationMode = false;

// Enhanced Global Variables
int atrHandle, ema5Handle, ema13Handle, ema21Handle, rsiHandle;
int dailyATRHandle, volumeHandle;
datetime lastTradeTime = 0;
datetime lastBarTime = 0;
int totalTrades = 0, winTrades = 0, consecutiveLosses = 0;
int todayTrades = 0;
double todayPnL = 0.0, dailyRisk = 0.0;
double lastATR = 0, dailyATRValue = 0;
double minLot = 0, maxLot = 0, lotStep = 0;
double adjustedFixedLot = 0;
MqlDateTime currentTime;

// Performance tracking
struct TradeStats {
    int spike_50_100;
    int spike_100_200; 
    int spike_200_400;
    int spike_400_plus;
    int wins_50_100;
    int wins_100_200;
    int wins_200_400; 
    int wins_400_plus;
    double best_profit;
    double worst_loss;
} stats;

//+------------------------------------------------------------------+
int OnInit() {
    Print("=== V75 1s Improved Spike Hunter v6.0 Starting ===");
    
    // Initialize symbol specifications
    minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    // Validate and adjust lot size
    adjustedFixedLot = NormalizeLot(FixedLot);
    if(adjustedFixedLot != FixedLot) {
        Print("Adjusted FixedLot from ", FixedLot, " to ", adjustedFixedLot);
    }
    
    // Create enhanced indicators
    atrHandle = iATR(_Symbol, PERIOD_M1, 14);
    dailyATRHandle = iATR(_Symbol, PERIOD_D1, 14);
    ema5Handle = iMA(_Symbol, PERIOD_M1, 5, 0, MODE_EMA, PRICE_CLOSE);
    ema13Handle = iMA(_Symbol, PERIOD_M1, 13, 0, MODE_EMA, PRICE_CLOSE);
    ema21Handle = iMA(_Symbol, PERIOD_M1, 21, 0, MODE_EMA, PRICE_CLOSE);
    rsiHandle = iRSI(_Symbol, PERIOD_M1, RSI_Period, PRICE_CLOSE);
    volumeHandle = iVolumes(_Symbol, PERIOD_M1, VOLUME_TICK);
    
    // Validate indicator creation
    if(atrHandle == INVALID_HANDLE || dailyATRHandle == INVALID_HANDLE || 
       ema5Handle == INVALID_HANDLE || ema13Handle == INVALID_HANDLE || 
       ema21Handle == INVALID_HANDLE || rsiHandle == INVALID_HANDLE ||
       volumeHandle == INVALID_HANDLE) {
        Print("ERROR: Failed to create one or more indicators");
        return(INIT_FAILED);
    }
    
    Sleep(3000); // Allow indicators to load
    
    PrintImprovedSettings();
    Print("=== Enhanced EA Ready - Quality over Quantity Approach ===");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
void OnTick() {
    // Check for new bar
    datetime currentBar = iTime(_Symbol, PERIOD_M1, 0);
    if(lastBarTime == currentBar) return;
    lastBarTime = currentBar;
    
    // Reset daily counters at start of new day
    TimeToStruct(TimeCurrent(), currentTime);
    static int lastDay = -1;
    if(currentTime.day != lastDay) {
        ResetDailyCounters();
        lastDay = currentTime.day;
    }
    
    // Pre-trade validations
    if(!PreTradeValidation()) return;
    
    // Get market data
    if(!GetEnhancedMarketData()) return;
    
    // Analyze and potentially trade
    AnalyzeEnhancedSignal();
    
    // Manage existing positions
    ManageAdvancedPositions();
}

//+------------------------------------------------------------------+
bool PreTradeValidation() {
    // Check if already in position
    if(PositionsTotal() > 0) return false;
    
    // Check daily trade limit
    if(todayTrades >= MaxTradesPerDay) {
        if(ShowDebug) Print("Daily trade limit reached: ", todayTrades);
        return false;
    }
    
    // Check daily risk limit
    if(MathAbs(todayPnL) >= (AccountInfoDouble(ACCOUNT_BALANCE) * MaxDailyRisk / 100.0)) {
        if(ShowDebug) Print("Daily risk limit reached. PnL: ", todayPnL);
        return false;
    }
    
    // Check consecutive losses
    if(consecutiveLosses >= MaxConsecutiveLosses) {
        if(ShowDebug) Print("Too many consecutive losses: ", consecutiveLosses);
        return false;
    }
    
    // Check cooldown period
    if((TimeCurrent() - lastTradeTime) < (CooldownMinutes * 60)) {
        return false;
    }
    
    // Check trading session
    if(UseSessionFilter && !IsGoodTradingSession()) {
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
bool IsGoodTradingSession() {
    TimeToStruct(TimeCurrent(), currentTime);
    int currentHour = currentTime.hour;
    
    // Check if within trading hours
    if(currentHour < StartHour || currentHour > EndHour) {
        if(ShowDebug) Print("Outside trading hours: ", currentHour);
        return false;
    }
    
    // Avoid first and last 30 minutes of session
    if(currentHour == StartHour && currentTime.min < 30) return false;
    if(currentHour == EndHour && currentTime.min > 30) return false;
    
    // Avoid news times (simplified - you can enhance this)
    if(AvoidNews) {
        // Avoid major news hours (can be enhanced with economic calendar)
        if(currentTime.day_of_week == 5 && currentHour >= 13 && currentHour <= 15) {
            return false; // Friday afternoon news
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
bool GetEnhancedMarketData() {
    double atr[], dailyATR[], ema5[], ema13[], ema21[], rsi[], volume[];
    
    ArraySetAsSeries(atr, true);
    ArraySetAsSeries(dailyATR, true);
    ArraySetAsSeries(ema5, true);
    ArraySetAsSeries(ema13, true);
    ArraySetAsSeries(ema21, true);
    ArraySetAsSeries(rsi, true);
    ArraySetAsSeries(volume, true);
    
    // Copy all indicator data
    if(CopyBuffer(atrHandle, 0, 0, 5, atr) < 5 ||
       CopyBuffer(dailyATRHandle, 0, 0, 2, dailyATR) < 2 ||
       CopyBuffer(ema5Handle, 0, 0, 5, ema5) < 5 ||
       CopyBuffer(ema13Handle, 0, 0, 5, ema13) < 5 ||
       CopyBuffer(ema21Handle, 0, 0, 5, ema21) < 5 ||
       CopyBuffer(rsiHandle, 0, 0, 5, rsi) < 5 ||
       CopyBuffer(volumeHandle, 0, 0, 5, volume) < 5) {
        if(ShowDebug) Print("Failed to get enhanced market data");
        return false;
    }
    
    lastATR = atr[1];
    dailyATRValue = dailyATR[0];
    
    return true;
}

//+------------------------------------------------------------------+
void AnalyzeEnhancedSignal() {
    // Get candle data (previous completed bar)
    double open = iOpen(_Symbol, PERIOD_M1, 1);
    double high = iHigh(_Symbol, PERIOD_M1, 1);
    double low = iLow(_Symbol, PERIOD_M1, 1);
    double close = iClose(_Symbol, PERIOD_M1, 1);
    
    if(open == 0 || high == 0 || low == 0 || close == 0) return;
    
    // Calculate enhanced spike metrics
    double candleRange = (high - low) / _Point;
    double bodySize = MathAbs(close - open) / _Point;
    double bodyRatio = candleRange > 0 ? bodySize / candleRange : 0;
    bool isBullish = close > open;
    
    double upperWick = (isBullish ? (high - close) : (high - open)) / _Point;
    double lowerWick = (isBullish ? (open - low) : (close - low)) / _Point;
    double atrPoints = lastATR / _Point;
    double atrMultiplier = atrPoints > 0 ? candleRange / atrPoints : 0;
    
    // Enhanced signal validation
    if(!IsHighQualitySpike(candleRange, bodyRatio, atrMultiplier, upperWick, lowerWick)) {
        return;
    }
    
    // Wait for confirmation (avoid immediate entry)
    if(MinutesAfterSpike > 0) {
        datetime candleTime = iTime(_Symbol, PERIOD_M1, 1);
        if((TimeCurrent() - candleTime) < (MinutesAfterSpike * 60)) {
            return;
        }
    }
    
    // Get confluence signals
    ENUM_ORDER_TYPE tradeType;
    double confidence = 0;
    if(!GetConfluenceSignal(isBullish, tradeType, confidence)) {
        return;
    }
    
    // Only trade high-confidence signals
    if(confidence < 0.7) {
        if(ShowDebug) Print("Signal confidence too low: ", DoubleToString(confidence, 2));
        return;
    }
    
    // Execute enhanced trade
    ExecuteEnhancedTrade(tradeType, candleRange, bodyRatio, atrMultiplier, confidence);
}

//+------------------------------------------------------------------+
bool IsHighQualitySpike(double range, double bodyRatio, double atrMult, double upperWick, double lowerWick) {
    // Basic size filters
    if(range < MinSpikeSize || range > MaxSpikeSize) {
        if(ShowDebug) Print("Spike size out of range: ", range);
        return false;
    }
    
    // Body ratio filter
    if(bodyRatio < MinBodyRatio || bodyRatio > MaxBodyRatio) {
        if(ShowDebug) Print("Body ratio out of range: ", DoubleToString(bodyRatio, 2));
        return false;
    }
    
    // ATR multiplier filter
    if(atrMult < MinATRMultiplier || atrMult > MaxATRMultiplier) {
        if(ShowDebug) Print("ATR multiplier out of range: ", DoubleToString(atrMult, 2));
        return false;
    }
    
    // Daily volatility filter
    if(UseVolatilityFilter) {
        double dailyATRPoints = dailyATRValue / _Point;
        if(dailyATRPoints < MinDailyATR || dailyATRPoints > MaxDailyATR) {
            if(ShowDebug) Print("Daily volatility out of range: ", dailyATRPoints);
            return false;
        }
    }
    
    // Wick analysis - avoid candles with excessive wicks
    double totalWicks = upperWick + lowerWick;
    if(totalWicks > (range * 0.6)) { // Wicks shouldn't be more than 60% of total range
        if(ShowDebug) Print("Excessive wicks detected");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
bool GetConfluenceSignal(bool isBullish, ENUM_ORDER_TYPE &tradeType, double &confidence) {
    confidence = 0;
    int positiveSignals = 0;
    int totalSignals = 0;
    
    // EMA trend analysis
    double ema5[], ema13[], ema21[];
    ArraySetAsSeries(ema5, true);
    ArraySetAsSeries(ema13, true);
    ArraySetAsSeries(ema21, true);
    
    CopyBuffer(ema5Handle, 0, 0, 3, ema5);
    CopyBuffer(ema13Handle, 0, 0, 3, ema13);
    CopyBuffer(ema21Handle, 0, 0, 3, ema21);
    
    bool strongUpTrend = ema5[1] > ema13[1] && ema13[1] > ema21[1];
    bool strongDownTrend = ema5[1] < ema13[1] && ema13[1] < ema21[1];
    
    totalSignals++;
    if((isBullish && strongUpTrend) || (!isBullish && strongDownTrend)) {
        positiveSignals++;
        confidence += 0.3;
    }
    
    // RSI momentum filter
    if(UseMomentumFilter) {
        double rsi[];
        ArraySetAsSeries(rsi, true);
        CopyBuffer(rsiHandle, 0, 0, 3, rsi);
        
        totalSignals++;
        if(isBullish && rsi[1] > 50 && rsi[1] < RSI_Overbought) {
            positiveSignals++;
            confidence += 0.25;
        }
        else if(!isBullish && rsi[1] < 50 && rsi[1] > RSI_Oversold) {
            positiveSignals++;
            confidence += 0.25;
        }
    }
    
    // Volume confirmation
    if(UseVolumeFilter) {
        double volume[];
        ArraySetAsSeries(volume, true);
        CopyBuffer(volumeHandle, 0, 0, 10, volume);
        
        // Compare current volume to average of last 5 bars
        double avgVolume = (volume[2] + volume[3] + volume[4] + volume[5] + volume[6]) / 5.0;
        
        totalSignals++;
        if(volume[1] > avgVolume * 1.2) { // 20% above average
            positiveSignals++;
            confidence += 0.2;
        }
    }
    
    // Structure analysis (simplified)
    if(UseStructureFilter) {
        double currentPrice = iClose(_Symbol, PERIOD_M1, 1);
        double high5 = iHigh(_Symbol, PERIOD_M1, iHighest(_Symbol, PERIOD_M1, MODE_HIGH, 20, 2));
        double low5 = iLow(_Symbol, PERIOD_M1, iLowest(_Symbol, PERIOD_M1, MODE_LOW, 20, 2));
        
        totalSignals++;
        // Avoid trading near strong resistance/support
        double distanceFromHigh = (high5 - currentPrice) / _Point;
        double distanceFromLow = (currentPrice - low5) / _Point;
        
        if(distanceFromHigh > 100 && distanceFromLow > 100) {
            positiveSignals++;
            confidence += 0.15;
        }
    }
    
    // Determine trade direction with contrarian bias for V75
    if(positiveSignals >= (totalSignals * 0.6)) { // At least 60% confluence
        // Use contrarian approach - trade against the spike
        tradeType = isBullish ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
        
        if(ShowDebug) {
            Print("High-quality signal: ", EnumToString(tradeType), 
                  " | Confidence: ", DoubleToString(confidence, 2),
                  " | Signals: ", positiveSignals, "/", totalSignals);
        }
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
void ExecuteEnhancedTrade(ENUM_ORDER_TYPE type, double spike, double bodyRatio, double atrMult, double confidence) {
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double price = (type == ORDER_TYPE_BUY) ? ask : bid;
    
    // Enhanced SL/TP calculation
    double baseDistance = MathMax(lastATR * 1.2, spike * _Point * 0.4);
    
    // Adjust based on confidence
    double confidenceMultiplier = 0.8 + (confidence * 0.4); // 0.8 to 1.2 range
    double slDistance = baseDistance * confidenceMultiplier;
    
    double sl = (type == ORDER_TYPE_BUY) ? price - slDistance : price + slDistance;
    double tp = (type == ORDER_TYPE_BUY) ? price + (slDistance * RRRatio) : price - (slDistance * RRRatio);
    
    double lot = CalculateEnhancedLotSize(slDistance / _Point, confidence);
    
    ulong ticket = SendEnhancedOrder(type, price, sl, tp, lot);
    if(ticket > 0) {
        lastTradeTime = TimeCurrent();
        totalTrades++;
        todayTrades++;
        
        // Update statistics
        UpdateTradeStats(spike, true);
        
        Print("ENHANCED TRADE: ", EnumToString(type), 
              " | Spike: ", DoubleToString(spike, 1),
              " | Confidence: ", DoubleToString(confidence, 2),
              " | ATR×: ", DoubleToString(atrMult, 2),
              " | Lot: ", lot, " | Ticket: ", ticket);
    }
}

//+------------------------------------------------------------------+
double CalculateEnhancedLotSize(double riskPips, double confidence) {
    if(UseFixedLot) {
        // Adjust fixed lot based on confidence
        double adjustedLot = adjustedFixedLot * (0.7 + confidence * 0.6); // 0.7x to 1.3x
        return NormalizeLot(MathMin(adjustedLot, MaxLot));
    }
    
    // Risk-based calculation with confidence adjustment
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = balance * RiskPercent / 100.0;
    
    // Adjust risk based on confidence and recent performance
    double performanceMultiplier = GetPerformanceMultiplier();
    riskAmount *= confidence * performanceMultiplier;
    
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double pointValue = tickValue * (_Point / tickSize);
    
    double calculatedLot = riskAmount / (riskPips * pointValue);
    calculatedLot = MathMin(calculatedLot, MaxLot);
    
    return NormalizeLot(calculatedLot);
}

//+------------------------------------------------------------------+
double GetPerformanceMultiplier() {
    if(totalTrades < 10) return 1.0;
    
    double winRate = (double)winTrades / totalTrades;
    
    if(winRate > 0.6) return 1.2;        // Increase size if performing well
    else if(winRate > 0.4) return 1.0;   // Normal size
    else if(winRate > 0.25) return 0.8;  // Reduce size if struggling
    else return 0.6;                     // Significantly reduce if poor performance
}

//+------------------------------------------------------------------+
void ManageAdvancedPositions() {
    for(int i = PositionsTotal()-1; i >= 0; i--) {
        ulong ticket = PositionGetTicket(i);
        if(!PositionSelectByTicket(ticket) || PositionGetString(POSITION_SYMBOL) != _Symbol) continue;
        
        double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
        double currentPrice = SymbolInfoDouble(_Symbol, 
            (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? SYMBOL_BID : SYMBOL_ASK);
        double currentSL = PositionGetDouble(POSITION_SL);
        double currentTP = PositionGetDouble(POSITION_TP);
        
        ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        
        // Move to breakeven
        if(BreakevenPips > 0) {
            double breakevenDistance = BreakevenPips * _Point;
            bool shouldMoveToBreakeven = false;
            
            if(posType == POSITION_TYPE_BUY && currentPrice > openPrice + breakevenDistance) {
                shouldMoveToBreakeven = true;
            }
            else if(posType == POSITION_TYPE_SELL && currentPrice < openPrice - breakevenDistance) {
                shouldMoveToBreakeven = true;
            }
            
            if(shouldMoveToBreakeven && currentSL != openPrice) {
                ModifyPosition(ticket, openPrice + (posType == POSITION_TYPE_BUY ? _Point : -_Point), currentTP);
                if(ShowDebug) Print("Moved to breakeven: ", ticket);
            }
        }
        
        // Trailing stop
        if(TrailStartPips > 0) {
            double trailStartDistance = TrailStartPips * _Point;
            double trailDistance = TrailPips * _Point;
            
            if(posType == POSITION_TYPE_BUY) {
                if(currentPrice > openPrice + trailStartDistance) {
                    double newSL = currentPrice - trailDistance;
                    if(newSL > currentSL + _Point) {
                        ModifyPosition(ticket, newSL, currentTP);
                        if(ShowDebug) Print("Trailing BUY SL to: ", newSL);
                    }
                }
            }
            else {
                if(currentPrice < openPrice - trailStartDistance) {
                    double newSL = currentPrice + trailDistance;
                    if(newSL < currentSL - _Point || currentSL == 0) {
                        ModifyPosition(ticket, newSL, currentTP);
                        if(ShowDebug) Print("Trailing SELL SL to: ", newSL);
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction &trans, const MqlTradeRequest &request, const MqlTradeResult &result) {
    if(trans.type == TRADE_TRANSACTION_DEAL_ADD && 
       (trans.deal_type == DEAL_TYPE_SELL || trans.deal_type == DEAL_TYPE_BUY)) {
        
        if(HistoryDealSelect(trans.deal)) {
            double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
            double volume = HistoryDealGetDouble(trans.deal, DEAL_VOLUME);
            
            todayPnL += profit;
            
            if(profit > 0) {
                winTrades++;
                consecutiveLosses = 0;
                stats.best_profit = MathMax(stats.best_profit, profit);
            } else {
                consecutiveLosses++;
                stats.worst_loss = MathMin(stats.worst_loss, profit);
            }
            
            Print("TRADE RESULT: ", (profit > 0) ? "WIN" : "LOSS", 
                  " | P&L: $", DoubleToString(profit, 2),
                  " | Consecutive Losses: ", consecutiveLosses);
            
            if(TrackDetailedStats) PrintEnhancedStats();
        }
    }
}

//+------------------------------------------------------------------+
void UpdateTradeStats(double spikeSize, bool isEntry) {
    if(!isEntry) return;
    
    if(spikeSize >= 50 && spikeSize < 100) stats.spike_50_100++;
    else if(spikeSize >= 100 && spikeSize < 200) stats.spike_100_200++;
    else if(spikeSize >= 200 && spikeSize < 400) stats.spike_200_400++;
    else if(spikeSize >= 400) stats.spike_400_plus++;
}

//+------------------------------------------------------------------+
double NormalizeLot(double lot) {
    if(lotStep == 0) return lot;
    double normalized = MathRound(lot / lotStep) * lotStep;
    return MathMax(minLot, MathMin(maxLot, normalized));
}

//+------------------------------------------------------------------+
ulong SendEnhancedOrder(ENUM_ORDER_TYPE type, double price, double sl, double tp, double lot) {
    lot = NormalizeLot(lot);
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lot;
    request.type = type;
    request.price = NormalizeDouble(price, _Digits);
    request.sl = NormalizeDouble(sl, _Digits);
    request.tp = NormalizeDouble(tp, _Digits);
    request.deviation = 50; // Reduced slippage
    request.magic = 789456;
    request.comment = "V75_Enhanced_v6.0";
    request.type_filling = ORDER_FILLING_IOC;
    
    bool success = OrderSend(request, result);
    
    if(!success && result.retcode == TRADE_RETCODE_INVALID_FILL) {
        request.type_filling = ORDER_FILLING_FOK;
        success = OrderSend(request, result);
    }
    
    return success ? result.order : 0;
}

//+------------------------------------------------------------------+
bool ModifyPosition(ulong ticket, double sl, double tp) {
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_SLTP;
    request.position = ticket;
    request.sl = NormalizeDouble(sl, _Digits);
    request.tp = NormalizeDouble(tp, _Digits);
    
    return OrderSend(request, result);
}

//+------------------------------------------------------------------+
void ResetDailyCounters() {
    todayTrades = 0;
    todayPnL = 0.0;
    Print("=== New Trading Day - Counters Reset ===");
}

//+------------------------------------------------------------------+
void PrintEnhancedStats() {
    double winRate = totalTrades > 0 ? (double)winTrades / totalTrades * 100.0 : 0;
    double avgProfit = totalTrades > 0 ? todayPnL / todayTrades : 0;
    
    Print("=== ENHANCED V75 TRADING STATS ===");
    Print("Total Trades: ", totalTrades, " | Wins: ", winTrades, " | Win Rate: ", DoubleToString(winRate, 1), "%");
    Print("Today's Trades: ", todayTrades, " | Today's P&L: $", DoubleToString(todayPnL, 2));
    Print("Best Profit: $", DoubleToString(stats.best_profit, 2), " | Worst Loss: $", DoubleToString(stats.worst_loss, 2));
    Print("Consecutive Losses: ", consecutiveLosses, " | Daily ATR: ", DoubleToString(dailyATRValue/_Point, 1), " points");
    
    // Spike size analysis
    Print("=== SPIKE SIZE PERFORMANCE ===");
    Print("50-100: ", stats.spike_50_100, " trades | Wins: ", stats.wins_50_100);
    Print("100-200: ", stats.spike_100_200, " trades | Wins: ", stats.wins_100_200);
    Print("200-400: ", stats.spike_200_400, " trades | Wins: ", stats.wins_200_400);
    Print("400+: ", stats.spike_400_plus, " trades | Wins: ", stats.wins_400_plus);
    Print("===============================");
}

//+------------------------------------------------------------------+
void PrintImprovedSettings() {
    Print("=== ENHANCED V75 SPIKE HUNTER SETTINGS ===");
    Print("Advanced Filtering: ", (UseAdvancedFiltering ? "ON" : "OFF"));
    Print("Spike Size Range: ", MinSpikeSize, " - ", MaxSpikeSize, " points");
    Print("Body Ratio Range: ", DoubleToString(MinBodyRatio, 2), " - ", DoubleToString(MaxBodyRatio, 2));
    Print("ATR Multiplier Range: ", DoubleToString(MinATRMultiplier, 1), "x - ", DoubleToString(MaxATRMultiplier, 1), "x");
    Print("Risk per Trade: ", DoubleToString(RiskPercent, 1), "% | Max Daily Risk: ", DoubleToString(MaxDailyRisk, 1), "%");
    Print("Fixed Lot: ", (UseFixedLot ? DoubleToString(adjustedFixedLot, 2) : "Risk-based"));
    Print("Risk-Reward Ratio: 1:", DoubleToString(RRRatio, 1));
    Print("Trading Hours: ", StartHour, ":00 - ", EndHour, ":00");
    Print("Max Trades/Day: ", MaxTradesPerDay, " | Cooldown: ", CooldownMinutes, " min");
    
    // Confluence filters status
    Print("=== CONFLUENCE FILTERS ===");
    Print("Volume Filter: ", (UseVolumeFilter ? "ON" : "OFF"));
    Print("Volatility Filter: ", (UseVolatilityFilter ? "ON" : "OFF"));
    Print("Momentum Filter: ", (UseMomentumFilter ? "ON" : "OFF"));
    Print("Structure Filter: ", (UseStructureFilter ? "ON" : "OFF"));
    Print("Session Filter: ", (UseSessionFilter ? "ON" : "OFF"));
    Print("News Avoidance: ", (AvoidNews ? "ON" : "OFF"));
    Print("=====================================");
}

//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    Print("=== V75 Enhanced Spike Hunter v6.0 Stopping ===");
    Print("Final Session Stats:");
    if(TrackDetailedStats) PrintEnhancedStats();
    
    // Release indicator handles
    IndicatorRelease(atrHandle);
    IndicatorRelease(dailyATRHandle);
    IndicatorRelease(ema5Handle);
    IndicatorRelease(ema13Handle);
    IndicatorRelease(ema21Handle);
    IndicatorRelease(rsiHandle);
    IndicatorRelease(volumeHandle);
    
    Print("=== EA Shutdown Complete ===");
}

//+------------------------------------------------------------------+